// 工具函数库

import { Decimal } from "decimal.js";
import { APP_CONFIG } from "./config";
import {
  decimal,
  toNumber,
  formatPercentage,
  formatCurrency as formatCurrencyDecimal,
} from "./decimal";
import { format, isValid, parseISO, isAfter } from "date-fns";
import { zhCN } from "date-fns/locale";
// 数字格式化 - 支持 Decimal 和 number
export const formatNumber = (
  number_: number | Decimal,
  decimals = 2,
  locale = "zh-CN"
): string => {
  if (number_ instanceof Decimal) {
    if (!number_.isFinite()) return "--";
    return toNumber(number_).toLocaleString(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });
  }

  if (Number.isNaN(number_)) return "--";

  return number_.toLocaleString(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

// 货币格式化 - 支持 Decimal 和 number
export const formatCurrency = (
  amount: number | Decimal,
  currency = "CNY",
  locale = "zh-CN"
): string => {
  if (amount instanceof Decimal) {
    if (!amount.isFinite()) return "--";
    return formatCurrencyDecimal(amount, currency, locale);
  }

  if (Number.isNaN(amount)) return "--";

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// 百分比格式化 - 支持 Decimal 和 number
export const formatPercent = (
  value: number | Decimal,
  decimals = 2
): string => {
  if (value instanceof Decimal) {
    if (!value.isFinite()) return "--";
    return formatPercentage(value, decimals);
  }

  if (Number.isNaN(value)) return "--";

  return `${formatNumber(value * 100, decimals)}%`;
};

// 日期格式化 - 使用 date-fns
export const formatDate = (
  date: string | Date,
  formatString = "yyyy-MM-dd"
): string => {
  try {
    const dateObject = typeof date === "string" ? parseISO(date) : date;
    if (!isValid(dateObject)) return "--";

    // 支持中文格式
    if (formatString === "YYYY年MM月DD日") {
      return format(dateObject, "yyyy年MM月dd日", { locale: zhCN });
    }

    // 转换常用格式
    const formatMap: Record<string, string> = {
      "YYYY-MM-DD": "yyyy-MM-dd",
      "YYYY/MM/DD": "yyyy/MM/dd",
      "MM/DD/YYYY": "MM/dd/yyyy",
      "DD/MM/YYYY": "dd/MM/yyyy",
    };

    const actualFormat = formatMap[formatString] || formatString;
    return format(dateObject, actualFormat, { locale: zhCN });
  } catch (error) {
    console.error("Date formatting error:", error);
    return "--";
  }
};

// 日期范围验证 - 使用 date-fns
export const validateDateRange = (
  startDate: string,
  endDate: string
): { isValid: boolean; error?: string } => {
  const start = parseISO(startDate);
  const end = parseISO(endDate);

  if (!isValid(start)) {
    throw new TypeError("开始日期格式不正确");
  }

  if (!isValid(end)) {
    throw new TypeError("结束日期格式不正确");
  }

  if (isAfter(start, end) || start.getTime() === end.getTime()) {
    throw new Error("开始日期必须早于结束日期");
  }

  const daysDiff = getDaysBetween(start, end);
  const maxDays = APP_CONFIG.validation.period.max;

  if (daysDiff > maxDays) {
    throw new Error(`投资期间不能超过${Math.floor(maxDays / 365)}年`);
  }

  return { isValid: true };
};

// 金额验证 - 支持 Decimal 和 number
export const validateAmount = (
  amount: number | Decimal,
  fieldName = "金额"
): { isValid: boolean; error?: string } => {
  const amountDecimal = decimal(amount);

  if (!amountDecimal.isFinite()) {
    throw new TypeError(`${fieldName}必须是数字`);
  }

  if (amountDecimal.lt(0)) {
    throw new Error("金额不能为负数");
  }

  const { min, max } = APP_CONFIG.validation.amount;
  const minDecimal = decimal(min);
  const maxDecimal = decimal(max);

  if (amountDecimal.lt(minDecimal)) {
    throw new Error("金额不能小于1元");
  }

  if (amountDecimal.gt(maxDecimal)) {
    throw new Error(`${fieldName}不能超过${formatCurrency(maxDecimal)}`);
  }

  return { isValid: true };
};

// 百分比验证 - 支持 Decimal 和 number
export const validatePercentage = (
  value: number | Decimal,
  fieldName = "百分比"
): { isValid: boolean; error?: string } => {
  const valueDecimal = decimal(value);

  if (!valueDecimal.isFinite()) {
    throw new TypeError(`${fieldName}必须是数字`);
  }

  const { min, max } = APP_CONFIG.validation.percentage;
  const minDecimal = decimal(min);
  const maxDecimal = decimal(max);

  if (valueDecimal.lt(minDecimal) || valueDecimal.gt(maxDecimal)) {
    throw new Error(`百分比必须在0-100之间`);
  }

  return { isValid: true };
};

import { differenceInMonths, differenceInDays } from "date-fns";

// 计算两个日期之间的月数 - 使用 date-fns
export const getMonthsBetween = (startDate: Date, endDate: Date): number => {
  return differenceInMonths(endDate, startDate);
};

// 计算两个日期之间的天数 - 使用 date-fns
export const getDaysBetween = (startDate: Date, endDate: Date): number => {
  return differenceInDays(endDate, startDate);
};

// 计算年化收益率 - 支持 Decimal 和 number
export const calculateAnnualizedReturn = (
  initialValue: number | Decimal,
  finalValue: number | Decimal,
  days: number | Decimal
): number => {
  const initial = decimal(initialValue);
  const final = decimal(finalValue);
  const daysDecimal = decimal(days);

  if (initial.lte(0) || final.lte(0) || daysDecimal.lte(0)) return 0;

  const years = daysDecimal.div(365);
  const result = final.div(initial).pow(decimal(1).div(years)).sub(1).mul(100);

  return toNumber(result);
};

// 计算波动率 - 支持 Decimal 和 number
export const calculateVolatility = (returns: (number | Decimal)[]): number => {
  if (returns.length < 2) return 0;

  const decimalReturns = returns.map((r) => decimal(r));

  // 计算平均值
  let sum = decimal(0);
  for (const r of decimalReturns) {
    sum = sum.add(r);
  }
  const mean = sum.div(decimalReturns.length);

  // 计算方差
  const squaredDifferences = decimalReturns.map((r) => r.sub(mean).pow(2));
  let varianceSum = decimal(0);
  for (const sq of squaredDifferences) {
    varianceSum = varianceSum.add(sq);
  }
  const variance = varianceSum.div(decimalReturns.length);

  // 年化波动率
  const volatility = variance.sqrt().mul(decimal(252).sqrt()).mul(100);

  return toNumber(volatility);
};

// 计算最大回撤 - 支持 Decimal 和 number
export const calculateMaxDrawdown = (values: (number | Decimal)[]): number => {
  if (values.length < 2) return 0;

  const decimalValues = values.map((v) => decimal(v));
  let maxDrawdown = decimal(0);
  let peak = decimalValues[0];

  for (const value of decimalValues) {
    if (value.gt(peak)) {
      peak = value;
    }

    const drawdown = peak.sub(value).div(peak);
    if (drawdown.gt(maxDrawdown)) {
      maxDrawdown = drawdown;
    }
  }

  return toNumber(maxDrawdown.mul(100));
};

// 计算夏普比率 - 支持 Decimal 和 number
export const calculateSharpeRatio = (
  annualizedReturn: number | Decimal,
  volatility: number | Decimal,
  riskFreeRate: number | Decimal = APP_CONFIG.backtest.riskMetrics.riskFreeRate
): number => {
  const returnDecimal = decimal(annualizedReturn);
  const volatilityDecimal = decimal(volatility);
  const riskFreeDecimal = decimal(riskFreeRate);

  if (volatilityDecimal.isZero()) return 0;

  const sharpe = returnDecimal
    .div(100)
    .sub(riskFreeDecimal)
    .div(volatilityDecimal.div(100));

  return toNumber(sharpe);
};

// 防抖函数
export const debounce = <T extends (...arguments_: unknown[]) => unknown>(
  function_: T,
  delay: number
): ((...arguments_: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;

  return (...arguments_: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => function_(...arguments_), delay);
  };
};

// 节流函数
export const throttle = <T extends (...arguments_: unknown[]) => unknown>(
  function_: T,
  delay: number
): ((...arguments_: Parameters<T>) => void) => {
  let lastCall = 0;

  return (...arguments_: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      function_(...arguments_);
    }
  };
};

// 深拷贝
export const deepClone = <T>(object: T): T => {
  if (object === null || typeof object !== "object") return object;

  if (object instanceof Date) return new Date(object) as T;
  if (Array.isArray(object)) return object.map((item) => deepClone(item)) as T;

  const cloned = {} as T;
  for (const key in object) {
    if (object.hasOwnProperty(key)) {
      cloned[key] = deepClone(object[key]);
    }
  }

  return cloned;
};

// 生成唯一ID
export const generateId = (prefix = "id"): string => {
  // Using crypto.randomUUID() would be better but requires Node.js 14+
  // Using a more deterministic approach for testing
  const timestamp = Date.now();
  const randomPart = Array.from({ length: 8 }, () => 
    Math.floor(Math.random() * 16).toString(16)
  ).join('');
  
  return `${prefix}_${timestamp}_${randomPart}`;
};

// 下载文件

// 复制到剪贴板

// 错误处理
export const handleError = (error: unknown, context = ""): string => {
  console.error(`Error in ${context}:`, error);

  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === "string") {
    return error;
  }

  return "未知错误";
};
