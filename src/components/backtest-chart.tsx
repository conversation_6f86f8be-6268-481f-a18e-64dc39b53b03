"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  ReferenceDot,
} from "recharts";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

import type { BacktestResult } from "@/types/fund";

import PerformanceComparisonChart from "./performance-comparison-chart";
import TransactionTable from "./transaction-table";

// 格式化数字显示
const formatNumber = (number_: number, decimals = 2): string => {
  return number_.toLocaleString("zh-CN", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

// 格式化百分比
const formatPercent = (number_: number): string => {
  return `${formatNumber(number_)}%`;
};

// 格式化金额
const formatCurrency = (number_: number): string => {
  return `¥${formatNumber(number_)}`;
};

// 获取收益率颜色
const getReturnColor = (value: number): string => {
  if (value > 0) return "text-green-600";
  if (value < 0) return "text-red-600";
  return "text-gray-600";
};

interface BacktestChartProperties {
  result: BacktestResult;
}

export default function BacktestChart({ result }: BacktestChartProperties) {
  const { performance, timeline } = result;

  // 准备图表数据
  const chartData = timeline.map((item) => ({
    date: item.date,
    dateFormatted: format(new Date(item.date), "MM/dd", { locale: zhCN }),
    value: item.value,
    totalInvestment: item.totalInvestment,
    investment: item.investment,
    return: item.return,
    netAssetValue: item.netAssetValue,
    shares: item.shares,
  }));

  // 获取买卖点数据
  const transactionPoints = timeline
    .filter((item) => item.investment !== 0)
    .map((item) => ({
      date: item.date,
      value: item.value,
      investment: item.investment,
      type: item.investment > 0 ? "buy" : "sell",
    }));

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length > 0) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">
            {format(new Date(label), "yyyy年MM月dd日", { locale: zhCN })}
          </p>
          <div className="space-y-1 text-sm">
            <p className="text-blue-600">
              资产价值: {formatCurrency(data.value)}
            </p>
            <p className="text-gray-600">
              累计投入: {formatCurrency(data.totalInvestment)}
            </p>
            <p className="text-purple-600">
              基金净值: {formatCurrency(data.netAssetValue)}
            </p>
            {data.investment !== 0 && (
              <p className={data.investment > 0 ? "text-green-600" : "text-red-600"}>
                {data.investment > 0 ? "买入" : "卖出"}: {formatCurrency(Math.abs(data.investment))}
              </p>
            )}
            <p className={`font-medium ${getReturnColor(data.return)}`}>
              收益率: {formatPercent(data.return)}
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  // 投资价值走势图组件
  const ValueTrendChart = () => {
    if (timeline.length === 0) return null;

    return (
      <ResponsiveContainer width="100%" height={400}>
        <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="dateFormatted"
            tick={{ fontSize: 12 }}
            interval="preserveStartEnd"
          />
          <YAxis
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `¥${(value / 1000).toFixed(0)}k`}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />

          {/* 累计投入线 */}
          <Line
            type="monotone"
            dataKey="totalInvestment"
            stroke="#94a3b8"
            strokeWidth={2}
            strokeDasharray="5 5"
            name="累计投入"
            dot={false}
          />

          {/* 资产价值线 */}
          <Line
            type="monotone"
            dataKey="value"
            stroke="#3b82f6"
            strokeWidth={3}
            name="资产价值"
            dot={false}
          />

          {/* 基金净值线 */}
          <Line
            type="monotone"
            dataKey="netAssetValue"
            stroke="#8b5cf6"
            strokeWidth={2}
            name="基金净值"
            dot={false}
            yAxisId="right"
          />

          {/* 买卖点标记 */}
          {transactionPoints.map((point, index) => (
            <ReferenceDot
              key={`${point.date}-${index}`}
              x={point.date}
              y={point.value}
              r={6}
              fill={point.type === "buy" ? "#10b981" : "#ef4444"}
              stroke="#ffffff"
              strokeWidth={2}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    );
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">回测结果</h3>

      {/* 关键指标 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">总收益率</div>
          <div
            className={`text-xl font-bold ${getReturnColor(performance.totalReturn)}`}
          >
            {formatPercent(performance.totalReturn)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">年化收益率</div>
          <div
            className={`text-xl font-bold ${getReturnColor(performance.annualizedReturn)}`}
          >
            {formatPercent(performance.annualizedReturn)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">最大回撤</div>
          <div className="text-xl font-bold text-red-600">
            {formatPercent(performance.maxDrawdown)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">夏普比率</div>
          <div className="text-xl font-bold text-gray-900">
            {formatNumber(performance.sharpeRatio)}
          </div>
        </div>
      </div>

      {/* 详细指标 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">累计投入</div>
          <div className="text-lg font-semibold text-gray-900">
            {formatCurrency(performance.totalInvestment)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">最终价值</div>
          <div className="text-lg font-semibold text-gray-900">
            {formatCurrency(performance.finalValue)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">波动率</div>
          <div className="text-lg font-semibold text-gray-900">
            {formatPercent(performance.volatility)}
          </div>
        </div>
      </div>

      {/* 收益率对比图表 */}
      <PerformanceComparisonChart result={result} />

      {/* 投资价值走势图 */}
      <div className="bg-white p-6 border border-gray-200 rounded-lg">
        <h4 className="text-md font-semibold text-gray-900 mb-4">
          投资价值走势图
          <span className="text-sm font-normal text-gray-500 ml-2">
            (绿点=买入，红点=卖出)
          </span>
        </h4>
        <ValueTrendChart />
      </div>

      {/* 交易记录表格 */}
      <TransactionTable result={result} />
    </div>
  );
}
