import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";

import TransactionTable from "./transaction-table";
import type { BacktestResult } from "@/types/fund";

const mockBacktestResult: BacktestResult = {
  strategy: "fixed_amount",
  fund: {
    id: "test-fund",
    name: "测试基金",
    code: "000001",
    type: "stock",
  },
  params: {
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    initialAmount: 10_000,
    monthlyAmount: 1000,
    frequency: "monthly" as const,
  },
  performance: {
    totalReturn: 15.5,
    annualizedReturn: 12.3,
    volatility: 18.2,
    sharpeRatio: 0.85,
    maxDrawdown: -8.5,
    totalInvestment: 22_000,
    finalValue: 25_410,
  },
  timeline: [
    {
      date: "2023-01-01",
      investment: 10_000,
      totalInvestment: 10_000,
      shares: 1000,
      value: 10_000,
      return: 0,
      netAssetValue: 10,
    },
    {
      date: "2023-02-01",
      investment: 1000,
      totalInvestment: 11_000,
      shares: 1100,
      value: 11_550,
      return: 5,
      netAssetValue: 10.5,
    },
    {
      date: "2023-03-01",
      investment: 1000,
      totalInvestment: 12_000,
      shares: 1200,
      value: 12_600,
      return: 5,
      netAssetValue: 10.5,
    },
  ],
};

describe("TransactionTable", () => {
  it("应该渲染交易记录表格", () => {
    render(<TransactionTable result={mockBacktestResult} />);

    expect(screen.getByText("交易记录")).toBeInTheDocument();
    expect(screen.getByText("共 3 笔交易")).toBeInTheDocument();
  });

  it("应该显示表格头部", () => {
    render(<TransactionTable result={mockBacktestResult} />);

    expect(screen.getByText("日期")).toBeInTheDocument();
    expect(screen.getByText("类型")).toBeInTheDocument();
    expect(screen.getByText("金额")).toBeInTheDocument();
    expect(screen.getByText("份额")).toBeInTheDocument();
    expect(screen.getByText("净值")).toBeInTheDocument();
    expect(screen.getByText("累计份额")).toBeInTheDocument();
    expect(screen.getByText("总价值")).toBeInTheDocument();
    expect(screen.getByText("收益率")).toBeInTheDocument();
  });

  it("应该显示交易记录", () => {
    render(<TransactionTable result={mockBacktestResult} />);

    // 检查第一笔交易（初始投资）
    expect(screen.getByText("2023-01-01")).toBeInTheDocument();
    expect(screen.getByText("买入")).toBeInTheDocument();
    expect(screen.getByText("¥10,000.00")).toBeInTheDocument();

    // 检查第二笔交易
    expect(screen.getByText("2023-02-01")).toBeInTheDocument();
    expect(screen.getAllByText("买入")).toHaveLength(3); // 所有交易都是买入
  });

  it("应该正确格式化货币", () => {
    render(<TransactionTable result={mockBacktestResult} />);

    expect(screen.getByText("¥10,000.00")).toBeInTheDocument();
    expect(screen.getByText("¥1,000.00")).toBeInTheDocument();
  });

  it("应该正确格式化百分比", () => {
    render(<TransactionTable result={mockBacktestResult} />);

    expect(screen.getByText("+0.00%")).toBeInTheDocument();
    expect(screen.getAllByText("+5.00%")).toHaveLength(2);
  });

  it("应该显示汇总信息", () => {
    render(<TransactionTable result={mockBacktestResult} />);

    expect(screen.getByText("总投入:")).toBeInTheDocument();
    expect(screen.getByText("¥22,000.00")).toBeInTheDocument();
    expect(screen.getByText("最终价值:")).toBeInTheDocument();
    expect(screen.getByText("¥25,410.00")).toBeInTheDocument();
    expect(screen.getByText("总收益:")).toBeInTheDocument();
    expect(screen.getByText("¥3,410.00")).toBeInTheDocument();
    expect(screen.getByText("总收益率:")).toBeInTheDocument();
    expect(screen.getByText("+15.50%")).toBeInTheDocument();
  });

  it("应该处理空交易记录", () => {
    const emptyResult = {
      ...mockBacktestResult,
      timeline: [],
    };

    render(<TransactionTable result={emptyResult} />);

    expect(screen.getByText("暂无交易记录")).toBeInTheDocument();
  });

  it("应该正确显示买入和卖出类型", () => {
    const resultWithSell = {
      ...mockBacktestResult,
      timeline: [
        ...mockBacktestResult.timeline,
        {
          date: "2023-04-01",
          investment: -500, // 卖出
          totalInvestment: 11_500,
          shares: 1150,
          value: 12_075,
          return: 5,
          netAssetValue: 10.5,
        },
      ],
    };

    render(<TransactionTable result={resultWithSell} />);

    expect(screen.getByText("卖出")).toBeInTheDocument();
    expect(screen.getAllByText("买入")).toHaveLength(3);
  });
});
