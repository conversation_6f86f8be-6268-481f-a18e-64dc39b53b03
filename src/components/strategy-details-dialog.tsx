"use client";

import * as Dialog from "@radix-ui/react-dialog";
import type { Strategy } from "@/types/fund";

interface StrategyDetailsDialogProperties {
  strategy: Strategy | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function StrategyDetailsDialog({
  strategy,
  open,
  onOpenChange,
}: StrategyDetailsDialogProperties) {
  if (!strategy) return null;

  const getRiskLevelColor = (level: Strategy["riskLevel"]) => {
    const colors = {
      low: "bg-green-100 text-green-800",
      medium: "bg-yellow-100 text-yellow-800",
      high: "bg-red-100 text-red-800",
    };
    return colors[level];
  };

  const getRiskLevelLabel = (level: Strategy["riskLevel"]) => {
    const labels = {
      low: "低风险",
      medium: "中风险",
      high: "高风险",
    };
    return labels[level];
  };

  const getComplexityColor = (complexity: Strategy["complexity"]) => {
    const colors = {
      beginner: "bg-blue-100 text-blue-800",
      intermediate: "bg-purple-100 text-purple-800",
      advanced: "bg-gray-100 text-gray-800",
    };
    return colors[complexity];
  };

  const getComplexityLabel = (complexity: Strategy["complexity"]) => {
    const labels = {
      beginner: "初级",
      intermediate: "中级",
      advanced: "高级",
    };
    return labels[complexity];
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-40" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl z-50 w-full max-w-2xl max-h-[90vh] overflow-auto">
          <div className="p-6">
            {/* 头部 */}
            <div className="flex items-center justify-between mb-4">
              <Dialog.Title className="text-xl font-semibold text-gray-900">
                {strategy.name}
              </Dialog.Title>
              <Dialog.Close asChild>
                <button
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                  aria-label="关闭"
                >
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </Dialog.Close>
            </div>

            {/* 标签 */}
            <div className="flex items-center space-x-2 mb-4">
              <span
                className={`text-xs px-2 py-1 rounded-full ${getRiskLevelColor(
                  strategy.riskLevel
                )}`}
              >
                {getRiskLevelLabel(strategy.riskLevel)}
              </span>
              <span
                className={`text-xs px-2 py-1 rounded-full ${getComplexityColor(
                  strategy.complexity
                )}`}
              >
                {getComplexityLabel(strategy.complexity)}
              </span>
            </div>

            {/* 描述 */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-2">策略描述</h3>
              <p className="text-sm text-gray-600">{strategy.description}</p>
            </div>

            {/* 参数说明 */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3">参数说明</h3>
              <div className="space-y-3">
                {Object.entries(strategy.parameterSchema).map(([key, parameter]) => (
                  <div key={key} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-900">
                        {parameter.label}
                      </span>
                      {parameter.required && (
                        <span className="text-xs text-red-500">必填</span>
                      )}
                    </div>
                    {parameter.description && (
                      <p className="text-xs text-gray-600 mb-2">
                        {parameter.description}
                      </p>
                    )}
                    <div className="text-xs text-gray-500">
                      类型: {parameter.type}
                      {parameter.min !== undefined && ` | 最小值: ${parameter.min}`}
                      {parameter.max !== undefined && ` | 最大值: ${parameter.max}`}
                      {parameter.defaultValue !== undefined && ` | 默认值: ${parameter.defaultValue}`}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 适用场景 */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-2">适用场景</h3>
              <div className="text-sm text-gray-600">
                {strategy.riskLevel === "low" && (
                  <p>适合风险承受能力较低的投资者，追求稳健收益。</p>
                )}
                {strategy.riskLevel === "medium" && (
                  <p>适合有一定风险承受能力的投资者，平衡风险与收益。</p>
                )}
                {strategy.riskLevel === "high" && (
                  <p>适合风险承受能力较强的投资者，追求较高收益。</p>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3">
              <Dialog.Close asChild>
                <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  关闭
                </button>
              </Dialog.Close>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
